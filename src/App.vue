<template>
  <simple-tabs v-model="activeKey" :tabs="tabs" />
</template>

<script>
import SimpleTabs from "./SimpleTabs.vue";

export default {
  name: "App",
  components: {
    SimpleTabs,
  },
  data: () => ({
    activeKey: "1",
    tabs: new Array(50).fill(0).map((_, i) => ({
      key: (i + 1).toString(),
      label: `Tab ${i + 1}`
    })),
  }),
};
</script>

<style>
body {
  padding: 10px;
}
</style>
