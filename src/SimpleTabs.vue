<template>
  <div class="__simple_tabs">
    <div class="container">
      <div class="outline"></div>
      <div class="prev" v-if="scrollWidth > panelsWidth">
        <i class="el-icon-arrow-left"></i>
      </div>
      <div class="panels" ref="panelsRef">
        <div
          class="panel_item"
          v-for="{ key, label } in tabs"
          :key="key"
          @click="$emit('change', key)"
          :class="{ active: key === value }"
        >
          <div class="label">
            {{ label }}
          </div>
        </div>
      </div>
      <div class="next" v-if="scrollWidth > panelsWidth" @click="next">
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SimpleTabs",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    tabs: {
      type: Array,
      default: () => [],
    },
  },
  data: () => ({
    panelsWidth: 0,
    scrollWidth: 0,
  }),
  methods: {
    getPanelsWidth() {
      if (!this.$refs.panelsRef) return 0;
      return this.$refs.panelsRef.offsetWidth;
    },
    getScrollWidth() {
      if (!this.$refs.panelsRef) return 0;
      return this.$refs.panelsRef.scrollWidth;
    },
    prev() {
      this.$refs.panelsRef.scrollLeft -= this.panelsWidth;
    },
    next() {
      this.$refs.panelsRef.scrollLeft += this.panelsWidth;
    },
  },
  mounted() {
    this.panelsWidth = this.getPanelsWidth();
    this.scrollWidth = this.getScrollWidth();
  },

  watch: {
    tabs() {
      this.panelsWidth = this.getPanelsWidth();
      this.scrollWidth = this.getScrollWidth();
    },
  },
};
</script>

<style lang="stylus">
.__simple_tabs {
  .container {
    position: relative;
    display: flex;
    align-items: center;

    .outline {
      width: 100%;
      position: absolute;
      bottom: 0;
      border-bottom: 1px solid #e4e7ed;
    }

    .prev, .next {
      height: 40px;
      line-height: 40px;
      cursor: pointer
      padding: 0 5px;
    }
  }

  .panels {
    border-radius: 4px 4px 0 0;
    border: 1px solid #e4e7ed;
    border-bottom: none;
    width: 100%;
    display: flex;
    overflow: hidden;

    .panel_item {
      border-left: 1px solid #e4e7ed;
      padding: 0 20px;
      cursor: pointer;
      color: #303133;
      flex-shrink: 0;
      white-space: nowrap;
      height: 40px;
      line-height: 40px;

      .label {
        font-size: 14px;
      }

      &:first-child {
        border-left: none;
      }

      &:hover {
        color: #409EFF;
      }
    }

    .active {
      color: #409EFF;
      border-bottom: 1px solid #FFF;
      z-index: 100;
    }
  }
}
</style>
