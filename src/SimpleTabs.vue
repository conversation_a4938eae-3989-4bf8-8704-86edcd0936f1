<template>
  <div class="__simple_tabs">
    <div class="container">
      <div class="outline"></div>
      <div class="panels">
        <div
          class="panel_item"
          v-for="{ key, label } in tabs"
          :key="key"
          @click="$emit('change', key)"
          :class="{ active: key === value }"
        >
          <div class="label">
            {{ label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SimpleTabs",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    tabs: {
      type: Array,
      default: () => [],
    },
  },
  data: () => ({}),
  mounted() {},
};
</script>

<style lang="stylus">
.__simple_tabs {
  .container {
    position: relative;

    .outline {
      width: 100%;
      position: absolute;
      bottom: 0;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .panels {
    border-radius: 4px 4px 0 0;
    border: 1px solid #e4e7ed;
    border-bottom: none;
    width: 100%

    .panel_item {
      border-left: 1px solid #e4e7ed;
      padding: 10px;
      cursor: pointer;
      color: 303133

      .label {
        font-size: 14px;
      }

      &:first-child {
        border-left: none;
      }
    }

    .active {
      color: #409EFF;
      border-bottom: 1px solid #FFF;
      z-index: 100;
    }
  }
}
</style>
